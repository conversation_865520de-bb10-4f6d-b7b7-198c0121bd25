import sqlite3
import logging
import configparser
from fastmcp import FastMCP, Context

# 配置日志
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)

# 创建FastMCP实例
logging.debug("创建FastMCP实例...")
mcp = FastMCP(
    "SQLiteDB",
    instructions="基于MCP协议的SQLite数据库查询服务"
)

# 读取配置
config = configparser.ConfigParser()
config.read('config_server.ini')
if not config.sections():
    raise RuntimeError("配置文件 config_server.ini 加载失败或为空")

# 获取配置文件中的参数
server_addr = config.get('server', 'IP_ADDRESS')
server_port = config.get('server', 'PORT')
server_trans_type = config.get('server', 'TRANSPORT_TYPE')

@mcp.tool(description="执行SQL查询")
async def query_db(sql: str, ctx: Context) -> str:
    logging.info(f"收到SQL查询请求: {sql}")
    """执行SQL查询

    Args:
        sql: SQL查询语句

    Returns:
        查询结果字符串
    """
    try:
        logging.debug(f"执行SQL查询: {sql}")
        conn = sqlite3.connect("database.db")
        cursor = conn.cursor()
        cursor.execute(sql)
        result = cursor.fetchall()
        logging.debug("SQL查询执行完成")
        return "\n".join(str(row) for row in result)
    except Exception as e:
        error_msg = f"Error: {str(e)}"
        logging.error(f"SQL查询执行失败: {error_msg}")
        return error_msg
    finally:
        conn.close()

@mcp.resource("db://schema", description="获取数据库表结构")
async def get_schema() -> list[str]:
    """获取数据库表结构

    Returns:
        表结构信息列表
    """
    conn = sqlite3.connect("database.db")
    cursor = conn.cursor()

    cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
    tables = cursor.fetchall()

    schema = []
    for table in tables:
        cursor.execute(f"PRAGMA table_info({table[0]})")
        columns = cursor.fetchall()
        schema.append(f"表 {table[0]}:")
        for column in columns:
            schema.append(f"  - {column[1]}: {column[2]}")

    conn.close()
    return schema

@mcp.prompt(description="生成SQL查询提示")
def sql_prompt(question: str) -> str:
    """生成SQL查询提示

    Args:
        question: 用户问题

    Returns:
        用于生成SQL的提示语
    """
    return f"""请将以下问题转换为SQL查询：
问题: {question}

可用的表结构:
- users(id, name, age, email)
- orders(id, user_id, product_name, price, order_date)

请生成标准的SQLite SQL语句，不要生成其他内容，只返回SQL语句本身。"""

if __name__ == "__main__":
    # 新版本fastMCP一键启动SSE服务（无需手动创建Starlette应用），但是fastMCP其实内置的仍然是uvicorn，通过它启动ASGI异步应用的高性能web服务
    import asyncio
    try:
        mcp.run(transport=server_trans_type,
                host=server_addr,
            port=int(server_port))
    except asyncio.CancelledError:
        logging.info("MCP 服务被取消，退出。")
    except Exception as e:
        logging.error(f"服务器异常：{str(e)}")


