from pymilvus import (
    connections,
    FieldSchema, CollectionSchema, DataType,
    Collection, utility
)
import numpy as np

# -------------------- 1. 连接 Milvus 服务 --------------------
connections.connect(
    alias="default",
    host="localhost",  # 若使用云服务或代理，替换为对应地址（如 api.wlai.vip）
    port="19530"
)

# -------------------- 2. 定义集合结构 --------------------
# 汽车信息字段 + 向量字段
fields = [
    FieldSchema(name="car_id", dtype=DataType.INT64, is_primary=True, auto_id=True),
    FieldSchema(name="brand", dtype=DataType.VARCHAR, max_length=50),   # 品牌（如 Tesla）
    FieldSchema(name="model", dtype=DataType.VARCHAR, max_length=100),  # 型号（如 Model 3）
    FieldSchema(name="year", dtype=DataType.INT16),                      # 生产年份
    FieldSchema(name="price", dtype=DataType.FLOAT),                    # 价格（万美元）
    FieldSchema(name="embedding", dtype=DataType.FLOAT_VECTOR, dim=128) # 特征向量（假设128维）
]

# 创建集合 Schema
schema = CollectionSchema(
    fields=fields,
    description="Collection for car vector search with metadata"
)

# 创建集合
collection_name = "car_vectors"
if utility.has_collection(collection_name):
    utility.drop_collection(collection_name)  # 若已存在则删除（测试用）

collection = Collection(name=collection_name, schema=schema)
print(f"Collection '{collection_name}' created.")

# -------------------- 3. 插入示例数据 --------------------
# 生成5辆汽车的元数据和随机向量
data = [
    ["Tesla", "Model S", 2023, 8.5],    # 品牌, 型号, 年份, 价格
    ["BMW", "iX", 2024, 9.2],
    ["Toyota", "Camry", 2022, 3.0],
    ["Ford", "Mustang", 2021, 4.5],
    ["Audi", "e-tron", 2023, 7.8]
]

vectors = [np.random.random(128).tolist() for _ in range(len(data))]  # 随机生成128维向量

# 插入数据（注意字段顺序需与 Schema 一致）
insert_data = [
    [item[0] for item in data],  # brand
    [item[1] for item in data],  # model
    [item[2] for item in data],  # year
    [item[3] for item in data],  # price
    vectors                      # embedding
]

insert_result = collection.insert(insert_data)
print(f"Inserted {len(insert_result.primary_keys)} car records.")

# -------------------- 4. 创建向量索引 --------------------
index_params = {
    "index_type": "IVF_FLAT",    # 平衡查询速度与精度
    "metric_type": "L2",         # 欧氏距离
    "params": {"nlist": 128}     # 聚类中心数
}

collection.create_index(
    field_name="embedding",
    index_params=index_params
)
print("Index created on 'embedding' field.")

# 加载集合到内存（搜索前必需）
collection.load()

# -------------------- 5. 执行混合搜索（向量+标量过滤） --------------------
# 模拟查询向量
query_vector = [np.random.random(128).tolist()]

# 搜索条件：价格低于5万美元且年份>=2022
search_params = {
    "metric_type": "L2",
    "params": {"nprobe": 10}     # 搜索聚类中心数
}

results = collection.search(
    data=query_vector,
    anns_field="embedding",
    param=search_params,
    limit=3,                     # 返回前3个相似结果
    expr="year >= 2022 and price < 5.0",  # 标量过滤条件
    output_fields=["brand", "model", "price"]  # 返回的字段
)

# 打印结果
print("\nSearch Results:")
for hits in results:
    for hit in hits:
        print(f"Brand: {hit.entity.get('brand')}, Model: {hit.entity.get('model')}, "
              f"Price: ${hit.entity.get('price')}万, Distance: {hit.distance:.4f}")

# -------------------- 6. 清理资源（可选） --------------------
connections.disconnect("default")
