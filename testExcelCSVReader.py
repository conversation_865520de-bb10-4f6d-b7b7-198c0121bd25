import csv
import pandas as pd


def convert_csv_to_txt(csv_file_path, txt_file_path):
    try:
        with open(csv_file_path, 'r', encoding='utf-8', newline='') as csvfile,
             open(txt_file_path, 'w', encoding='utf-8') as txtfile:
            reader = csv.reader(csvfile)
            next(reader)  # 跳过标题行
            for row in reader:
                question = row[0]
                case_analysis = row[1]
                txtfile.write(f'问题: {question}\n')
                txtfile.write(f'案例分析: {case_analysis}\n')
                txtfile.write('####\n')
         print(f"成功将 {csv_file_path} 转换为 {txt_file_path}")
    except FileNotFoundError:
        print(f"错误：未找到文件 {csv_file_path}")
    except IndexError:
        print("错误：CSV 文件中的行格式不符合预期，可能缺少列。")
    except Exception as e:
        print(f"发生未知错误：{e}")

def excel_to_formatted_txt(excel_file, output_txt_file):

    # 读取Excel文件
    df = pd.read_excel(excel_file)

    # 打开输出文件
    with open(output_txt_file, 'w', encoding='utf-8') as f:
        # 遍历每一行
        for index, row in df.iterrows():
            # 写入格式化的内容
            f.write(f"问题: {row.iloc[0]}\n")
            f.write(f"病例分析: {row.iloc[1]}\n")

            # 添加分隔符
            f.write("####\n")

if name == "main":
    excel_file = 'OncologyQA.xlsx'
    csv_file = 'OncologyQA.csv'
    csv_txt_file = 'csvTotxt.txt'
    exce_txt_file = 'excelTotxt1.txt'
    convert_csv_to_txt(csv_file, csv_txt_file)

    excel_to_formatted_txt(excel_file, exce_txt_file)