:::::::::::::::::::::::::::::: 用于启动chatmodel_call_test.py程序::::::::::::::::::
rem 功能描述：
rem 主要用来验证大模型调用接口，实现一次简单的大模型调用

:::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::
rem header part, prepare running environment and variables
@chcp 65001 >nul
@echo off
setlocal

set "cur_path=%cd%"
echo "working directory is set to %cur_path%"
cd /d "%cur_path%"
call D:\Work\python\venv310\Scripts\activate.bat

rem body part, main working snippet
python chatmodel_call_test.py

endlocal

pause