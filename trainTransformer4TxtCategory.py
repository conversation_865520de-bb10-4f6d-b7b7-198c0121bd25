# 文本分类
# 用随机数据模拟一个二分类问题

import torch
import torch.nn as nn
import torch.optim as optim

# ✅ 超参数
seq_len = 10    # 序列长度
d_model = 32    # 嵌入维度
nhead = 4       # 注意力头数
num_layers = 2  # Transformer 层数
num_classes = 2 # 二分类
epochs = 20
batch_size = 16

# ✅ 模拟数据 (1000 条序列)
X = torch.randn(1000, seq_len, d_model)
y = torch.randint(0, 2, (1000,))

# ✅ Transformer 分类模型
class TransformerClassifier(nn.Module):
    def __init__(self, d_model, nhead, num_layers, num_classes):
        super().__init__()
        encoder_layer = nn.TransformerEncoderLayer(d_model=d_model, nhead=nhead)
        self.transformer_encoder = nn.TransformerEncoder(encoder_layer, num_layers=num_layers)
        self.fc = nn.Linear(d_model, num_classes)

    def forward(self, x):
        # PyTorch 的 Transformer 期望输入形状: (seq_len, batch, d_model)
        x = x.permute(1, 0, 2)
        encoded = self.transformer_encoder(x)
        # 取最后一个 token 的表示
        last_token = encoded[-1]
        return self.fc(last_token)

model = TransformerClassifier(d_model, nhead, num_layers, num_classes)

criterion = nn.CrossEntropyLoss()
optimizer = optim.Adam(model.parameters(), lr=0.001)

# ✅ 训练循环
for epoch in range(epochs):
    for i in range(0, len(X), batch_size):
        x_batch = X[i:i+batch_size]
        y_batch = y[i:i+batch_size]

        outputs = model(x_batch)
        loss = criterion(outputs, y_batch)

        optimizer.zero_grad()
        loss.backward()
        optimizer.step()

    print(f"Epoch {epoch+1}/{epochs}, Loss: {loss.item():.4f}")
