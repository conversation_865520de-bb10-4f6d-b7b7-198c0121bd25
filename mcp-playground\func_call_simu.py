# 示例文件，展示如何正确格式化JSON内容

# 原始内容（未格式化）
# ather", "parameters": {"location": "{location}", "date": "{date}"}, "result": {"weather": "天气状况", "temperature": "温度"}} 
# 若无法提供信息，输出{"name": "get_weather", "parameters": {"location": "{location}", "date": "{date}"}, "result": {"error": "无法提供天气信息"}}

# 正确格式化的JSON内容 - 天气信息模板
weather_function_template = {
    "name": "get_weather", 
    "parameters": {
        "location": "{location}", 
        "date": "{date}"
    }, 
    "result": {
        "weather": "天气状况", 
        "temperature": "温度"
    }
}

# 当无法提供信息时的错误格式 - 错误模板
weather_function_error_template = {
    "name": "get_weather", 
    "parameters": {
        "location": "{location}", 
        "date": "{date}"
    }, 
    "result": {
        "error": "无法提供天气信息"
    }
}

# 将其转换为JSON字符串的示例
import json

print("天气信息模板:")
print(json.dumps(weather_function_template, ensure_ascii=False, indent=2))

print("\n天气信息错误模板:")
print(json.dumps(weather_function_error_template, ensure_ascii=False, indent=2))