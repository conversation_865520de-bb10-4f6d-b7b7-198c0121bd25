import sqlite3
import logging
import configparser
from typing import Optional, Any
from fastmcp import FastMCP, Context
from starlette.applications import Starlette
from mcp.server.sse import SseServerTransport
from starlette.requests import Request
from starlette.routing import Mount, Route

config = configparser.ConfigParser()
config.read('config_server.ini')
if not config.sections():
    raise RuntimeError("配置文件 config.ini 加载失败或为空")

# 获取配置文件中的API_KEY
server_addr = config.get('server', 'IP_ADDRESS')
server_port = config.get('server', 'PORT')
server_trans_type = config.get('server', 'TRANSPORT_TYPE')

# 配置日志
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)

# 创建FastMCP实例
logging.debug("开始创建MCP服务...")
mcp = FastMCP(
    "MCP-DB-Demo",
    instructions="基于MCP协议的SQLite数据库查询服务",
    debug=
    True # 启用调试模式[3](@ref)
)

# building MCP suites
# example for tools
# async def query_db(sql: str) -> str:
# 数据库工具（自动注入Context）
@mcp.tool(description="查询数据库")
async def query_db(sql: str, ctx: Context) -> str:
    logging.info(f"query statement: {sql}")
    """执行SQL查询

    Args:
        sql: SQL查询语句

    Returns:
        查询结果字符串
    """
    try:
        logging.debug(f"connecting to database")
        conn = sqlite3.connect("database.db")
        cursor = conn.cursor()
        cursor.execute(sql)
        result = cursor.fetchall()
        logging.debug("查询成功!")
        return "\n".join(str(row) for row in result)
    except Exception as e:
        error_msg = f"Error: {str(e)}"
        logging.error(f"查询失败 : {error_msg}")
        return error_msg
    finally:
        conn.close()

# example for resource function
# @mcp.resource("db://schema", description="生成数据库表结构描述")
#资源定义（使用URI模板规范[10](@ref)）
@mcp.resource("schema://tables", description="生成数据库表结构描述")
async def get_schema() -> list[str]:
    """获取数据库表结构

    Returns:
        表结构信息列表
    """
    conn = sqlite3.connect("database.db")
    cursor = conn.cursor()

    cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
    tables = cursor.fetchall()

    schema = []
    for table in tables:
        cursor.execute(f"PRAGMA table_info({table[0]})")
        columns = cursor.fetchall()
        schema.append(f"Table {table[0]}:")
        for column in columns:
            schema.append(f"  - {column[1]}: {column[2]}")

    conn.close()
    return schema

#example usage for prompt
# # 提示模板（支持参数化[3](@ref)）
@mcp.prompt(description="根据用户输入的信息生成SQL查询提示")
def generate_sql_prompt(user_input: str) -> str:
    """生成SQL查询提示

    Args:
        question: 用户问题

    Returns:
        用于生成SQL的提示语
    """
    return f"""请将以下问题转换为SQL语句：
问题: {user_input}

可用的表结构:
- users(id, name, age, email)
- orders(id, user_id, product_name, price, order_date)

请生成标准的SQLite数据库查询的SQL语句，不要生成其他内容，只返回SQL语句本身。
    """

# 创建高并发ASGI应用
# dicarded。 # 新版本fastMCP一键启动SSE服务（无需手动创建Starlette应用[2](@ref)[4](@ref)）
def create_starlette_app(mcp_server, *, debug: bool = False) -> Starlette:
    """
    创建一个Starlette应用程序，用于通过SSE协议提供MCP服务器服务。
    
    参数:
        mcp_server: MCP服务器实例，用于处理消息通信
        debug: 是否启用调试模式，默认为False
    
    返回值:
        配置好的Starlette应用实例
    """
    sse = SseServerTransport("/messages/")

    async def handle_sse(request: Request) -> None:
        """
        处理SSE连接请求的异步函数。
        
        建立SSE连接并协调MCP服务器与客户端之间的消息传输。
        """
        async with sse.connect_sse(
            request.scope,
            request.receive,
            request.send,  # noqa: SLF00
        ) as (read_stream, write_stream):
            await mcp_server.run(
                read_stream,
                write_stream,
                mcp_server.create_initialization_options()
            )

    # 配置 Starlette 应用，管理消息路由 （包含SSE路由和消息处理路由）
    # 支持/sse/路由连接请求
    # 挂载/messages/端点，处理POST消息
    return Starlette(
        debug=debug,
        routes=[
            Route("/sse/", endpoint=handle_sse),
            Mount("/messages/", app = sse.handle_post_message),
        ],
    )

if __name__ == "__main__":
    # 新版本fastMCP一键启动SSE服务（无需手动创建Starlette应用），但是fastMCP其实内置的仍然是uvicorn，通过它启动ASGI异步应用的高性能web服务
    import asyncio
    try:
        mcp.run(transport=server_trans_type,
                host=server_addr,
            port=int(server_port))
    except asyncio.CancelledError:
        logging.info("MCP 服务被取消，退出。")


