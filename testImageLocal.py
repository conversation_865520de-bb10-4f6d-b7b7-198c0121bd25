import base64
import json
import os
from openai import OpenAI

def encode_image(image_path):
    with open(image_path, "rb") as image_file:
        return base64.b64encode(image_file.read()).decode("utf-8")

client = OpenAI(
    api_key="sk-a62610acd124403b9b3acc8bf73199ab",
    base_url="https://dashscope.aliyuncs.com/compatible-mode/v1",
)

# retrive data from image
result =[]
for fileName in ["4.png", "5.png"]:

    completion = client.chat.completions.create(
        model="qwen-vl-max-latest",
        messages=[
            {
                "role": "system",
                "content": [{"type": "text", "text": "You are a helpful assistant."}],
            },
            {
                "role": "user",
                "content": [
                    {
                        "type": "image_url",
                        "image_url": {"url": f"data:image/png;base64,{encode_image(fileName)}"},
                    },
                    {"type": "text", "text": """
                    提取图中内容，按照json格式输出如下，注意只输出纯json字符就行，不要带有换行符
                    {
                    "product_name": "xxxx",
                    "product_type": "xxxx",
                    "shelf_life": "xxxx",
                    "ingredients": "xxxx",
                    "product_standard_code": "xxxx",
                    "storage_conditions": "xxxx",
                    "food_production_license_number": "xxxx",
                    "production_date": "xxxx",
                    "manufacturer": "xxxx",
                    "address": "xxxx",
                    "phone": "xxxx",
                    "fax": "xxxx",
                    "place_of_origin": "xxxx"
                    }
                    """},
                ],
            }
        ],

    )
    result.append(completion.choices[0].message.content)

print(completion.choices[0].message.content)

# save into mysql db
db_config = ""
conn = mysql.connector.connect(**db_config)
cursor = conn.cursor()
for item in result:
    try:
        data = json.loads(item)
        insert_query = """
        INSERT INTO products (product_name, product_type, shelf_life, ingredients,
        product_standard_code, storage_conditions, food_production_license_number,
        production_date, manufacturer, address, phone, fax, place_of_origin)
        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        """

        values = (
        data.get("product_name"),
        data.get("product_type"),
        data.get("shelf_life"),
        data.get("ingredients"),
        data.get("product_standard_code"),
        data.get("storage_conditions"),
        data.get("food_production_license_number"),
        data.get("production_date"),
        data.get("manufacturer"),
        data.get("address"),
        data.get("phone"),
        data.get("fax"),
        data.get("place_of_origin")
        )
        cursor.execute(insert_query, values)
        conn.commit()
        print(f"成功插入数据：{data.get('product_name')}")

    except Exception as e:
        print(f"Error: {e}")
        conn.close()