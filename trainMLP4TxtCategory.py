# 文本分类
# 用随机数据模拟一个二分类问题

# 1. 导入依赖
import torch
import torch.nn as nn
import torch.optim as optim

# 2. 定义超参数
input_dim = 20   # 输入特征维度
hidden_dim = 64  # 隐藏层大小
output_dim = 2   # 二分类
epochs = 50
batch_size = 32

# ✅ 生成模拟数据 (1000 条)
X = torch.randn(1000, input_dim)
y = torch.randint(0, 2, (1000,))  # 0 或 1

# ✅ 定义 MLP 模型
class MLP(nn.Module):
    def __init__(self, input_dim, hidden_dim, output_dim):
        super().__init__()
        self.layers = nn.Sequential(
            nn.Linear(input_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, output_dim)
        )

    def forward(self, x):
        return self.layers(x)

model = MLP(input_dim, hidden_dim, output_dim)

# ✅ 损失函数 & 优化器
criterion = nn.CrossEntropyLoss()
optimizer = optim.Adam(model.parameters(), lr=0.001)

# ✅ 训练循环
for epoch in range(epochs):
    for i in range(0, len(X), batch_size):
        x_batch = X[i:i+batch_size]
        y_batch = y[i:i+batch_size]

        # 前向传播
        outputs = model(x_batch)
        loss = criterion(outputs, y_batch)

        # 反向传播
        optimizer.zero_grad()
        loss.backward()
        optimizer.step()

    if (epoch + 1) % 10 == 0:
        print(f"Epoch {epoch+1}/{epochs}, Loss: {loss.item():.4f}")
