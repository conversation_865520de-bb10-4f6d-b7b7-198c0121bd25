from pymilvus import connections, FieldSchema, CollectionSchema, DataType, Collection

# 连接到 Milvus 服务器（默认本地）
connections.connect(alias="default", host="localhost", port="19530")[3]

# 定义集合字段（汽车信息 + 向量）
fields = [
    FieldSchema(name="car_id", dtype=DataType.INT64, is_primary=True, auto_id=True),  # 自增主键[9](@ref)
    FieldSchema(name="brand", dtype=DataType.VARCHAR, max_length=50),                 # 品牌（如 "Tesla"）
    FieldSchema(name="model", dtype=DataType.VARCHAR, max_length=100),                # 型号（如 "Model 3"）
    FieldSchema(name="year", dtype=DataType.INT16),                                   # 生产年份
    FieldSchema(name="price", dtype=DataType.FLOAT),                                  # 价格（万美元）
    FieldSchema(name="embedding", dtype=DataType.FLOAT_VECTOR, dim=128)               # 汽车特征向量（假设维度128）[2](@ref)[4](@ref)
]

# 创建集合 Schema
schema = CollectionSchema(
    fields=fields,
    description="Collection for storing car vector information with metadata"[8]
)

# 创建集合
collection_name = "car_vectors"
collection = Collection(name=collection_name, schema=schema)
print(f"Collection '{collection_name}' created successfully.")