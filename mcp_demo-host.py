import asyncio
from mcp_demo_client import MCP_Client
from openai import OpenAI
import json
import logging
from typing import Any
import configparser

config = configparser.ConfigParser()
config.read('config_client.ini')

# 获取配置文件中的API_KEY
api_key = config.get('model', 'API_KEY')
base_url = config.get('model', 'BASE_URL')
model_name = config.get('model', 'MODEL_NAME')

server_addr = config.get('server', 'IP_ADDRESS')
server_port = config.get('server', 'PORT')

# Configure logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

async def format_resource_content(content: Any) -> str:
    """Format resource content"""
    if isinstance(content, (str, int, float)):
        return str(content)
    elif isinstance(content, (list, dict)):
        return json.dumps(content, ensure_ascii=False, indent=2)
    return str(content)

async def main():
    logging.info("Starting MCP Host program...")
    client = MCP_Client(host=server_addr, port=server_port)
    openAIclient = OpenAI(api_key=api_key, base_url=base_url)

    try:
        # Connect to MCP server
        logging.info("Connecting to MCP server {server_addr}, begin...")
        await client.connect()
        logging.info("Connected to MCP server successfully!")

        # Get database schema
        logging.debug("Getting database schema...")
        async for content in client.get_resource("schema://tables"):
            formatted = await format_resource_content(content)
            logging.info(f"Database schema info.: \n {formatted}")

        # List available tools
        tools = await client.list_tools()
        print(f"Available tools: {[tool.tool_name for tool in tools]}")
        logging.info("Available tools:"+str(tools))

        while True:
            # Get user question
            user_question = input("\nEnter your question (q to quit): ")
            if user_question.lower() == 'q':
                break

            # Get SQL prompt
            logging.info(f"User question: {user_question}")
            prompt = await client.get_prompt("generate_sql_prompt", user_input=user_question)

            # Get tools description in OpenAI format, type is ToolDef
            openai_tools = [
                {
                    "type": "function",
                    "function": {
                        "name": tool.tool_name,
                        "description": tool.tool_desc,
                        "parameters": {
                            "type": "object",
                            "properties": {
                                param.par_name: {
                                    "type": param.par_type,
                                     "description": param.par_desc
                                        } for param in tool.tool_pars 
                            },
                            "required": [param.par_name for param in tool.tool_pars if param.required == True]
                        }
                    }
                }for tool in tools
            ]

            # Generate SQL query using OpenAI with MCP tools
            logging.info("Generating SQL query statement with llm model ...")
            prompt_text_4query = prompt.messages[0].content.text
            response = openAIclient.chat.completions.create(
                model=model_name,
                messages=[
                    {
                        "role": "system",
                        "content": "你是一个SQL查询助手，请根据用户的问题生成合适的SQL查询语句。"
                    },
                    {
                        "role": "user",
                        "content": prompt_text_4query
                    },
                ],
                tools=openai_tools,
                tool_choice={
                    "type": "function",
                    "function": {"name": "query_db"}
                }
            )

            # 自动模式（默认）
            # tool_choice="auto"  # 可以调用零个、一个或多个函数
            #
            # # 强制模式
            # tool_choice="required"  # 必须调用至少一个函数
            #
            # # 指定函数，强制调用特定函数
            # tool_choice={
            #     "type": "function",
            #     "function": {"name":"get_weather"}
            # } # 强制调用 get_weather 函数

            # ChatCompletion({'id': 'e92a220d-a1e1-4a79-b24f-f7140fa9a64a', 'choices':[Choice ({'finish_reason': 'tool_calls', 'index': 0, logprobs': None, message=ChatCompletionMessage (contents='', refusal=None, role='assistant', audio=None, function_call=None, tool_calls=[ChatCompletionMessageToolCall(id='call_0_a5a4250b-3bea-48b1-a457-9e352fffalda6', function=Function(arguments={'sql':"SELECT orders.* FROM orders JOIN users ON orders.user_id = users.id WHERE users.name = '\张三\'"}', name='query_db'), type='function', index=0)]))], created=1746146800, model='deepseek-chat', object='chat.completion', service_tier=None, system_fingerprint='fp_8802369eea_prod0425fp8', usage=CompletionUsage(completion_tokens=37, prompt_tokens=178, total_tokens=215, completion_tokens_details=None, prompt_tokens_details=PromptTokensDetails(audio_tokens=None, cached_tokens=128), prompt_cache_hit_tokens=128,prompt_cache_miss_tokens=50))
            logging.info(f"Response: {response}")
            
            tool_call = response.choices[0].message.tool_calls[0]
            func_name = tool_call.function.name  # 模型返回的函数名
            func_args = json.loads(tool_call.function.arguments)  # 模型返回的参数

            
            sql_args = json.loads(response.choices[0].message.tool_calls[0].function.arguments)
            sql_query = sql_args["sql"]
            logging.info(f"Generated SQL query: {sql_query}")

            # 调用对应工具, 执行 SQL 查询
            available_tool_names = [t.tool_name for t in tools] # 防御式编程
            if func_name not in available_tool_names:
                logging.error(f"模型返回了未知的工具: {func_name}")
            else:
                #result = await client.call_tool(func_name, func_args) 
                # 节约调试精力，直接给定函数和参数，正常情况下应该使用大模型返回来的函数名和参数
                result = await client.call_tool("query_db", {"sql": sql_query})
                logging.info(f"Result: {result.content}")

            # Explain result using OpenAI
            explanation = openAIclient.chat.completions.create(
                model=model_name,
                messages=[{
                    "role": "system",
                    "content": "你是一个数据库查询结果解释助手，请用中文解释查询结果。"
                },
                {
                    "role": "user",
                    "content": f"请用通俗易懂的中文解释这个查询结果:\n{result}"
                }]
            )
            logging.info("Result explanation:")
            logging.info(explanation.choices[0].
            message.content)

    except Exception as e:
        logging.error(f"Error occurred: {str(e)}",
        exc_info=True)
    finally:
        # Disconnect
        await client.disconnect()
        logging.info("Disconnected from MCP server")

if __name__ == "__main__":
    asyncio.run(main())
    