import sqlite3


def create_database():
    # 连接到数据库 (如果不存在则创建)
    conn = sqlite3.connect('database.db')
    cursor = conn.cursor()

    # 创建用户表
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS users (
        id INTEGER PRIMARY KEY,
        name TEXT NOT NULL,
        age INTEGER,
        email TEXT
    )
    ''')

    # 创建订单表
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS orders (
        id INTEGER PRIMARY KEY,
        user_id INTEGER,
        product_name TEXT NOT NULL,
        price REAL,
        order_date text,
        FOREIGN KEY (user_id) REFERENCES users(id)
    )
    ''')

    # 插入示例用户数据
    users = [
        (1, '张三', 25, 'zhang<PERSON>@example.com'),
        (2, '李四', 30, '<EMAIL>'),
        (3, '王五', 35, '<EMAIL>')
    ]
    cursor.executemany('INSERT OR REPLACE INTO users (id, name, age, email) VALUES (?, ?, ?, ?)', users)

    # 插入示例订单数据
    orders = [
        (1, 1, '笔记本电脑', 6999.99, '2025-04-01'),
        (2, 1, '手机', 4999.99, '2025-04-15'),
        (3, 2, '平板电脑', 3999.99, '2025-04-20'),
    ]
    cursor.executemany('INSERT OR REPLACE INTO orders (id, user_id, product_name, price, order_date) VALUES (?, ?, ?, ?, ?)', orders)

    # 提交事务
    conn.commit()
    # 关闭数据库连接
    conn.close()

if __name__ == '__main__':
    create_database()
    print("数据库和表已创建，并插入了示例数据。")