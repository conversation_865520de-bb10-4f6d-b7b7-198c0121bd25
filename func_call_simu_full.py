# 使用提示词模拟像 function calling 的格式化响应，明确通过 prompt 的方式让模型结构化输出函数名 + 参数 + 结果
# 建议使用纯文本生成的模型进行测试，例如 GPT-3.5-turbo, qwen-turbo 或者 qwen coder 系列
# 目标，通过这种方式了解有function calling 的模型是如何实现此功能的
# 测试结果：
# 模型：qwen-turbo
# 输入：北京明天的天气怎么样。
# 输出：Json格式化后的内容

import openai
import configparser
import json
from datetime import datetime

# 添加异常处理以更好地调试配置文件读取问题
try:
    config = configparser.ConfigParser()
    config.read('config_notools.ini')

    # 获取配置文件中的OpenAI API 密钥
    api_key = config.get('model', 'API_KEY')
    base_url = config.get('model', 'BASE_URL')
    model_name = config.get('model', 'MODEL_NAME')
except Exception as e:
    print(f"配置文件读取错误: {e}")
    exit(1)

# 定义提示词和函数描述
function_call_prompts = [
    {
        "name": "get_weather_info",
        "keyword": ["天气"],
        "prompt_text":
            """
            {
                "name": "get_weather_info", 
                "parameters": {
                    "location": "{location}", 
                    "date": "{date}"}, 
                "result": {"weather": "天气状况", "temperature": "温度"}
            } 
            若无法提供信息，输出
            {
                "name": "get_weather_info", 
                "parameters": {"location": "{location}", "date": "{date}"}, 
                "result": {"error": "未查询到天气信息"}
            }
            """,
        "parameters": ["location", "date"]
    },
    {
        "name": "get_order_info",
        "keyword": ["订单"],
        "prompt_text":
            """
            {
                "name": "get_order_info", 
                "parameters": {
                    "cusomter_name": "{cusomter_name}"}, 
                    "result": {
                        "order_id": "订单号",
                        "product_name": "产品名称", 
                        "product_id": "产品ID", 
                        "product_price": "产品价格", 
                        "product_count": "产品数量", 
                        "order_time": "订单时间", 
                        "order_status": "订单状态", 
                        "order_total": "订单总额", 
                        "order_address": "订单地址", 
                        "order_phone": "客户电话"
                    }
            }
            若无法提供信息，输出{
                "name": "get_order_info", 
                "parameters": {"cusomter_name": "{cusomter_name}"}, 
                "result": {"error": "未查询到客户订单信息"}}
            """,
        "parameters": ["cusomter_name"]
    }
]

# 构建主提示词
master_prompt = "以下是不同功能的回复格式要求：\n"
for prompt in function_call_prompts:
    master_prompt += f"当问题包含 {' '.join(prompt['keyword'])} 时，按以下格式回复：\n"
    master_prompt += prompt['prompt_text'] + "\n"
master_prompt += "若问题不匹配任何功能，输出 {'error': '未找到匹配的功能'}"

# 模拟用户输入
user_questions = [
    "北京明天的天气怎么样?",
    "查一下中国船舶的船板订单信息?",
    "今天股市行情怎么样?"
]

print(f"正在使用模型: {model_name}")
print(f"Base URL: {base_url}")

client = openai.OpenAI(api_key=api_key, base_url=base_url)

# ==== 1. 定义真实执行的函数 ====
def get_weather_info(location, date):
    # 模拟天气数据（真实情况可调用API）
    return {
        "weather": "晴转多云",
        "temperature": "26°C"
    }

def get_order_info(cusomter_name):
    # 模拟订单数据（真实情况可查数据库）
    return {
        "order_id": "ORD20250808001",
        "product_name": "船板钢材",
        "product_id": "SHIPSTEEL-001",
        "product_price": "5200元/吨",
        "product_count": 100,
        "order_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        "order_status": "已发货",
        "order_total": "520000元",
        "order_address": "上海宝山区长江路99号",
        "order_phone": "13888888888"
    }

# ==== 2. 循环处理用户问题 ====
for question in user_questions: 
    full_prompt = f"{master_prompt}\n用户询问：{question}" 
    
    try:
        response = client.chat.completions.create(
            model=model_name,
            messages=[
                {"role": "system", "content": "你是一个有用的助手，请严格按照指定格式模拟数据进行回复"},
                {"role": "user", "content": full_prompt}
            ],
            max_tokens=300,
            temperature=0.1
        )

        response_text = response.choices[0].message.content.strip()
        print(f"\n用户问题: {question}\n模型回复: {response_text}")

        # ==== 3. 解析模型返回的 JSON ====
        try:
            parsed_json = json.loads(response_text)
            func_name = parsed_json.get("name")
            params = parsed_json.get("parameters", {})

            if func_name == "get_weather_info":
                result = get_weather_info(params.get("location"), params.get("date"))
                parsed_json["result"] = result
            elif func_name == "get_order_info":
                result = get_order_info(params.get("cusomter_name"))
                parsed_json["result"] = result

            print("真实执行后的结果:", json.dumps(parsed_json, ensure_ascii=False, indent=2))
        except json.JSONDecodeError:
            print("❌ 模型输出不是合法 JSON，无法解析执行")

    except Exception as e:
        print(f"调用API时出错: {e}")
        if hasattr(e, 'response'):
            print(f"响应内容: {e.response}")