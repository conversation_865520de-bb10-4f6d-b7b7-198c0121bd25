import openai
import json
import configparser
import random
from datetime import date, timedelta

cfgFileName = 'config.ini'

# 定义工具描述
tools = [
    {
        "type": "function",
        "function": {
            "name": "get_customer_order_info",
            "description": "根据客户名称查询客户订单的生成进度信息",
            "parameters": {
                "type": "object",
                "properties": {
                    "customer_name": {
                        "type": "string",
                        "description": "客户名称"
                    }
                },
                "required": ["customer_name", ]
            }
        }
    },
    {
        "type": "function",
        "function": {
            "name": "get_material_info",
            "description": "根据钢种查询产品材质信息",
            "parameters": {
                "type": "object",
                "properties": {
                    "steel_grade": {
                        "type": "string",
                        "description": "钢种"
                    }
                },
                "required": ["steel_grade"]
            }
        }
    }
]

def load_config(path='config.ini'):
    config = configparser.ConfigParser()
    if not config.read(path):
        raise FileNotFoundError(f"配置文件 {path} 不存在或无法读取")
    if 'model' not in config:
        raise RuntimeError("配置文件缺少 [model] 部分")
    try:
        return {
            "api_key": config.get('model', 'API_KEY'),
            "base_url": config.get('model', 'BASE_URL'),
            "model_name": config.get('model', 'MODEL_NAME')
        }
    except (configparser.NoOptionError, configparser.NoSectionError) as e:
        raise RuntimeError(f"配置项读取失败: {e}")

cfg = load_config(cfgFileName)

# 获取配置文件中的API_KEY
api_key = cfg["api_key"]
base_url = cfg["base_url"]
model_name = cfg["model_name"]

# 获取客户订单信息
def get_customer_order_info(cusotmer_name):
    # 这里只是示例，实际中需要根据数据库的客户订单表查询
    order_info = {
        "customer": cusotmer_name,
        "due_date": (date.today() + timedelta(days=random.randint(10, 20))).strftime('%Y-%m-%d'),
        "order_weight_in_ton": random.randint(2000, 3500),
        "material_type": random.choice(["普通碳钢", "超低碳钢", "微合金钢", "低合金钢", "合金钢"])
    }
    return json.dumps(order_info)

# 根据钢种查询产品材质信息
def get_material_info(steel_grade):
    # 这里只是示例，实际中需要根据数据库的基础数据表获取材料信息
    order_info = {
        "grade_code": steel_grade,
        "material_type": random.choice(["普通碳钢", "超低碳钢", "微合金钢", "低合金钢", "合金钢"])
    }
    return json.dumps(order_info)

steel_grade = random.choice(["Q235", "Q345", "45",])
#user_input = f"{steel_grade}属于哪类材质的钢 ?"
user_input = f"客户中国船舶的船板订单生产状况怎么样了 ?"

client = openai.OpenAI(api_key=api_key, base_url=base_url)

try:
#response = openai.ChatCompletion.create( 此接口在opeai>=1.0.0不再支持, 如需兼容旧版本请使用pip install openai==0.28
    response = client.chat.completions.create(
        #model="gpt-4-1106-preview",
        model = model_name,
        messages=[
            {"role": "system", "content": "你是一个钢铁冶金专家，请根据输入的问题找出合适的工具。"},
            {"role": "user", "content": user_input}
        ],
        tools=tools,
        tool_choice = "auto"
    )
except openai.OpenAIError as e:
    print(f"在第一次模型调用时失败: {e}")
    exit(1)

# 解析响应
message = response.choices[0].message
if message.tool_calls:
    tool_call = message.tool_calls[0]
    function_name = tool_call.function.name
    parameters = json.loads(tool_call.function.arguments)  # 注意：arguments 是 JSON 字符串

    if function_name == "get_customer_order_info":
        customer_name = parameters["customer_name"]
        retStr = get_customer_order_info(customer_name)
    elif function_name == "get_material_info":
        grade_code = parameters["steel_grade"]
        retStr = get_material_info(grade_code)
        
    # 这里因为我们用的是reasoner推理模型，我们需要去除掉reasoning_content无效字段
    # 转换 message 为 dict 并移除 reasoning_content
    original_message = response.choices[0].message.model_dump()

    # 过滤掉 reasoning_content 字段（如果有）
    if "reasoning_content" in original_message:
        del original_message["reasoning_content"]


    # 再次调用 API 并将函数结果作为输入
    try:
        _response = client.chat.completions.create(
            model=model_name,
            messages=[
                {"role": "user", "content": user_input},
                original_message,  # 这条 message 是 ChatCompletionMessage 对象，OpenAI SDK 会自动处理
                {
                    "role": "tool",
                    "tool_call_id": tool_call.id,   # 注意：要提供 tool_call_id
                    "name": function_name,
                    "content": retStr
                }
            ]
        )
    except openai.OpenAIError as e:
        print(f"在第二次模型调用时失败: {e}")
        exit(1)

    print(f"用户输入问题: {user_input}")
    print(f"模型调用结果: {_response.choices[0].message.content}")

