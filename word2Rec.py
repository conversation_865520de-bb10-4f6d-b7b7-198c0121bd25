import tensorflow as tf
from tensorflow.keras.layers import TextVectorization, Embedding
from tensorflow.keras.models import Sequential
from tensorflow.keras import Input
import numpy as np

# Step 1: 创建词汇数据集（tf.data.Dataset）
vocabulary = tf.data.Dataset.from_tensor_slices(["dog", "his", "is", "old", "two"])
# Step 2: 创建 TextVectorization 层
vectorize_layer = TextVectorization(max_tokens=10000, output_mode='int', output_sequence_length=6)
vectorize_layer.adapt(vocabulary.batch(64))  # 进行 adapt

# Step 3: 创建使用 vectorization 的模型
layermodel = Sequential()
layermodel.add(Input(shape=(1,), dtype=tf.string))  # 输入必须为 (batch_size, 1)
layermodel.add(vectorize_layer)

# ✅ 正确输入格式：Python 原生字符串构成的二维列表（注意 shape）
# input_data = [
#     ["his dog is two years old"],
#     ["my dog is three years old"],
#     ["he has a cat"]
# ]

# 或者也可以使用 tf.constant 进行类型转换
input_data = tf.constant([
    ["his dog is two years old"],
    ["my dog is three years old"],
    ["he has a dog"]
])

# 确认是否构建成功
print("built:", vectorize_layer.built)  # 应该是 True

# 打印词汇表（看看 adapt 是否生效）
print("Vocabulary:", vectorize_layer.get_vocabulary())

# Step 4: 模型预测
output_array = layermodel.predict(input_data)
print("Text vectorization output:")
print(output_array)

# Step 5: 构建 Embedding 示例模型
model = Sequential()
model.add(Embedding(input_dim=100, output_dim=2))  # input_dim 是词汇大小，output_dim 是嵌入维度, 生成一个词汇表大小为 100、每个词嵌入为 2维的矩阵。
# input_array = np.array([[6, 7, 5], [1, 7, 5], [1, 1, 1]])
input_array = np.array(output_array)
model.compile(optimizer='rmsprop', loss='mse')
output_array = model.predict(input_array)
print("Embedding output:")
print(output_array)

from sklearn.manifold import TSNE
import matplotlib.pyplot as plt

# 从 embedding 层提取全部的权重（即所有词的向量）
word_vectors = model.layers[0].get_weights()[0]  # shape: (100, 2)
print(word_vectors.shape)
print(word_vectors)
# labels = [word for word, _ in vectorize_layer.get_vocabulary()]
# vocab = vectorize_layer.get_vocabulary()
# word_indices = [vectorize_layer(word).numpy()[0] for word in vocab]
# word_vectors_subset = word_vectors[word_indices]

# 若维度大于2才执行 TSNE
if word_vectors.shape[1] > 2:
    tsne = TSNE(n_components=2, random_state=0)
    reduced = tsne.fit_transform(word_vectors)
else:
    reduced = word_vectors

# 可视化（使用前5个词作为示例）
labels = ["dog", "his", "is", "old", "two", "cat"]
indices = [vectorize_layer(word).numpy()[0] for word in labels]
print(indices)
reduced_subset = reduced[indices]
print(reduced_subset)

plt.figure(figsize=(8, 8))
plt.scatter(reduced_subset[:, 0], reduced_subset[:, 1], s=50)

for i, word in enumerate(labels):
    plt.annotate(word, (reduced_subset[i, 0], reduced_subset[i, 1]))

plt.title("Word Embedding Visualization")
plt.grid(True)
plt.show()


# ... existing code ...
def plot_with_tsne(word_vectors, labels):
    """
    使用t-SNE算法对词向量进行降维并在二维平面上可视化展示
    
    参数:
        word_vectors: 词向量矩阵，每行代表一个词的向量表示
        labels: 与词向量对应的标签列表，用于在图中标识每个点
    
    返回值:
        无
    """
    # 使用t-SNE算法将高维词向量降维到二维空间
    tsne = TSNE(n_components=2, random_state=0)
    reduced = tsne.fit_transform(word_vectors)
    
    # 创建可视化图形窗口
    plt.figure(figsize=(12, 12))
    # 绘制降维后的词向量散点图
    plt.scatter(reduced[:, 0], reduced[:, 1], s=2, alpha=0.6)
    
    # 为每个点添加对应的标签注释
    for i, word in enumerate(labels):
        plt.annotate(word, (reduced[i, 0], reduced[i, 1]))
    
    # 设置图形标题并显示
    plt.title("Word Embedding Visualization")
    plt.show()
    plot_with_tsne(word_vectors, labels)
# ... existing code ...
# 假设 word_vectors 是一个 (num_words, embedding_dim) 的 numpy 矩阵
# labels 是对应的词

# tsne = TSNE(n_components=2, random_state=0)
# reduced = tsne.fit_transform(word_vectors)

# plt.figure(figsize=(12, 12))
# plt.scatter(reduced[:, 0], reduced[:, 1], s=2, alpha=0.6)
# plt.title("Word Embeddings Visualized with t-SNE")
# plt.show()


class Word2Vec(tf.keras.Model):
    def __init__(self, vocab_size, embedding_dim):
      """
      初始化Word2Vec模型
      
      Args:
          vocab_size (int): 词汇表大小，用于确定嵌入层的输入维度
          embedding_dim (int): 嵌入向量的维度，用于确定嵌入层的输出维度
      """
      super(Word2Vec, self).__init__()
      # 创建目标词嵌入层，用于将目标词转换为嵌入向量
      self.target_embedding = tf.keras.layers.Embedding(vocab_size, embedding_dim, input_length=1)
      # 创建上下文词嵌入层，用于将上下文词转换为嵌入向量
      self.context_embedding = tf.keras.layers.Embedding()