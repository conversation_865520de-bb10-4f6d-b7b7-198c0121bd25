# This workflow will install Python dependencies, run tests and lint with a single version of Python
# For more information see: https://docs.github.com/en/actions/automating-builds-and-tests/building-and-testing-python

name: 天气预报推送

on:
  schedule:
    # 设置启动时间，为 UTC 时间, UTC23点 对应北京时间早7点
    - cron : '00 23 * * *'
  workflow_dispatch:

permissions:
  contents: read

jobs:
  build:

    runs-on: ubuntu-latest
    env:
      TZ: Asia/Shanghai
    steps:
    - uses: actions/checkout@v3
    - name: Set up Python 3.11
      uses: actions/setup-python@v3
      with:
        python-version: "3.11"
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        if [ -f requirements.txt ]; then pip install -r requirements.txt; fi
    - name: Run weather Report
      run: |
        python weather_report.py
      env:
        APP_ID: ${{ secrets.APP_ID }}
        APP_SECRET: ${{ secrets.APP_SECRET }}
        OPEN_ID: ${{ secrets.OPEN_ID }}
        TEMPLATE_ID: ${{ secrets.TEMPLATE_ID }}
