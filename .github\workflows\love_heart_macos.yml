# This workflow will install Python dependencies, run tests and lint with a single version of Python
# For more information see: https://docs.github.com/en/actions/automating-builds-and-tests/building-and-testing-python

name: 画爱心macos版

on:
  workflow_dispatch:

permissions:
  contents: read

jobs:
  pyinstaller-build:
    runs-on: macos-latest
    steps:
      - name: Create Executable
        uses: sayyid5416/pyinstaller@v1
        with:
          python_ver: '3.12'
          spec: 'love_heart.py'
          upload_exe_with_name: 'love_heart'
          options: --onefile, --name "love_heart", --windowed, 
