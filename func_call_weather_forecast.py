import openai
import json
import configparser
import random
from datetime import date, timedelta

# 定义工具描述
tools = [
    {
        "type": "function",
        "function": {
            "name": "get_weather_forecast",
            "description": "查询指定地点和日期的天气预报信息",
            "parameters": {
                "type": "object",
                "properties": {
                    "location": {
                        "type": "string",
                        "description": "要查询天气的地点"
                    },
                    "date": {
                        "type": "string",
                        "description": "要查询天气的日期"
                    }
                },
                "required": ["location", "date"]
            }
        }
    }
]

config = configparser.ConfigParser()
config.read('config.ini')
if not config.sections():
    raise RuntimeError("配置文件 config.ini 加载失败或为空")

# 获取配置文件中的API_KEY
api_key = config.get('model', 'API_KEY')
base_url = config.get('model', 'BASE_URL')
model_name = config.get('model', 'MODEL_NAME')

# 定义一个函数，用于查询天气
def get_weather_forecast(location, date):
    # 这里只是示例，实际中需要调用真实的天气 API
    weather_info = {
        "location": location,
        "date": date,
        "weather": random.choice(["Sunny", "Cloudy", "Rainy", "Storm"]),
        "temperature": f"{random.randint(20, 35)}°C"
    }
    return json.dumps(weather_info)

tomorrow = (date.today() + timedelta(days=1)).strftime('%Y-%m-%d')
user_input = f"北京明天（{tomorrow}）的天气怎么样?"

client = openai.OpenAI(api_key=api_key, base_url=base_url)

try:
    #response = openai.ChatCompletion.create( 此接口在opeai>=1.0.0不再支持, 如需兼容旧版本请使用pip install openai==0.28
    response = client.chat.completions.create(
        #model="gpt-4-1106-preview",
        model = model_name,
        messages=[{"role": "user", "content": user_input}],
        tools=tools,
        tool_choice = "auto"
    )
except openai.OpenAIError as e:
    print(f"在第一次模型调用时失败: {e}")
    exit(1)

# 解析响应
if hasattr(response.choices[0], "message") and response.choices[0].message.tool_calls:
    tool_call = response.choices[0].message.tool_calls[0]
    function_name = tool_call.function.name
    parameters = json.loads(tool_call.function.arguments)  # 注意：arguments 是 JSON 字符串

    if function_name == "get_weather_forecast":
        location = parameters["location"]
        date = parameters["date"]
        function_response = get_weather_forecast(location, date)
        
        # 这里因为我们用的是reasoner推理模型，我们需要去除掉reasoning_content无效字段
        # 转换 message 为 dict 并移除 reasoning_content
        original_message = response.choices[0].message.model_dump()

        # 过滤掉 reasoning_content 字段（如果有）
        if "reasoning_content" in original_message:
            del original_message["reasoning_content"]


        # 再次调用模型并将函数结果作为输入
        try:
            _response = client.chat.completions.create(
                model=model_name,
                messages=[
                    {"role": "user", "content": user_input},
                    original_message,  # 这条 message 是 ChatCompletionMessage 对象，OpenAI SDK 会自动处理
                    {
                        "role": "tool",
                        "tool_call_id": tool_call.id,   # 注意：要提供 tool_call_id
                        "name": function_name,
                        "content": function_response
                    }
                ]
            )
        except openai.OpenAIError as e:
            print(f"在第二次模型调用时失败: {e}")
            exit(1)
    
        print(f"用户输入问题: {user_input}")
        print(f"模型调用结果: {_response.choices[0].message.content}")



