# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This repository contains Python implementations of Model Context Protocol (MCP) clients and servers, primarily focused on demonstrating database query capabilities through the MCP protocol. The main components include:

1. MCP Client (`mcp_demo_client.py`) - Connects to MCP servers and interacts with tools
2. MCP Server (`mcp_demo-server.py`) - Exposes SQLite database querying capabilities as MCP tools
3. Function calling simulations - Various examples of simulating function calling behavior with different models

## Key Files and Components

### Core MCP Implementation
- `mcp_demo_client.py` - MCP client implementation with tool listing and invocation capabilities
- `mcp_demo-server.py` - MCP server implementation with SQLite database tools
- `config_client.ini` and `config_server.ini` - Configuration files for client/server settings

### Function Calling Examples
- `func_call_simu.py` - Simulates function calling using prompt engineering
- `func_call_simu_full.py` - Extended function calling simulation
- `func_call_steel_buzz.py` - Steel industry related function calling example
- `func_call_weather_forecast.py` - Weather forecast function calling example

### Configuration and Setup
- `config.ini` - Main configuration file for model settings
- `database.db` - SQLite database for demonstration
- `create_database.py` - Script to initialize the database

## Common Development Tasks

### Running the MCP Server
```bash
python mcp_demo-server.py
```

### Running the MCP Client
```bash
python mcp_demo_client.py
```

### Testing Function Calling Simulations
```bash
python func_call_simu.py
```

## Code Architecture

### MCP Client Architecture
The client uses the `mcp.client.session.ClientSession` to communicate with MCP servers via SSE transport. Key components:
- `ToolDef` - Represents tool definitions with parameters
- `ToolParameter` - Describes individual tool parameters
- `ToolInvocationResult` - Wraps tool invocation results

### MCP Server Architecture
The server uses `FastMCP` framework to expose tools, resources, and prompts:
- Tools are decorated with `@mcp.tool()` 
- Resources are decorated with `@mcp.resource()`
- Prompts are decorated with `@mcp.prompt()`
- Database operations are implemented using SQLite

### Configuration Management
Configuration files use the standard `configparser` module:
- Client configuration in `config_client.ini`
- Server configuration in `config_server.ini`
- Model configuration in `config.ini`

## Dependencies
- `fastmcp` - Fast MCP implementation framework
- `mcp` - Core MCP protocol library
- `openai` - For model API interactions
- `sqlite3` - Database operations