import openai
from dotenv import load_dotenv
import json
import os
from datetime import datetime

# 加载环境变量（API密钥）
load_dotenv()
openai.api_key = os.getenv("OPENAI_API_KEY")

# 示例函数1：获取天气（模拟实现）
def get_current_weather(location, unit="celsius"):
    """
    模拟天气API调用
    实际应用时应替换为真实API调用
    """
    # 这里模拟可能的返回结果
    mock_data = {
        "location": location,
        "temperature": "22" if unit == "celsius" else "72",
        "unit": unit,
        "forecast": ["sunny", "cloudy"],
        "humidity": 65,
        "timestamp": datetime.now().isoformat()
    }
    return {
        "success": True,
        "data": mock_data,
        "error": None
    }

def get_stock_price(symbol, period="1d"):
    """
    模拟股票API调用
    返回示例:
      - 成功时返回股票数据
      - 符号无效时返回错误
    """
    valid_symbols = ["AAPL", "MSFT", "GOOGL"]
    if symbol not in valid_symbols:
        return {
            "success": False,
            "error": "Invalid stock symbol",
            "data": None
        }

    # 模拟数据
    return {
        "success": True,
        "data": {
            "symbol": symbol,
            "price": 185.32,
            "currency": "USD",
            "period": period,
            "history": [182.1, 184.5, 185.3]
        },
        "error": None
    }


tools = [
    {
        "type": "function",
        "action": {
            "name": "get_current_weather",
            "description": "获取指定位置的当前天气信息",
            "parameters": {
                "type": "object",
                "properties": {
                    "location": {"type": "string", "description": "城市名称"},
                    "unit": {"type": "string", "enum": ["celsius", "fahrenheit"]}
                },
                "required": ["location"]
            }
        }
    },
    {
        "type": "function",
        "action": {
            "name": "get_stock_price",
            "description": "获取股票价格信息",
            "parameters": {
                "type": "object",
                "properties": {
                    "symbol": {"type": "string", "description": "股票代码"},
                    "period": {"type": "string", "enum": ["1d", "1w", "1m"]}
                },
                "required": ["symbol"]
            }
        }
    }
]

def process_function_call(response):
    """
    处理模型返回的函数调用请求
    返回格式示例:
    - 当需要函数调用时返回 {"type": "function", "name": "...", "args": {...}}
    - 当普通回复时返回 {"type": "message", "content": "..."}
    """
    message = response.choices[0].message
    if not message.get("function_call"):
        return {
            "type": "message",
            "content": message["content"]
        }

    return {
        "type": "function",
        "name": message["function_call"]["name"],
        "args": json.loads(message["function_call"]["arguments"])
    }


# 聊天循环
def chat_loop():
    while True:
        user_input = input("\n用户: ")
        if user_input.lower() == 'exit':
            break

        # 第一步：模型判断是否需要调用函数
        response = openai.ChatCompletion.create(
            model="gpt-3.5-turbo-0613",
            messages=[{"role": "user", "content": user_input}],
            tools=tools,
            tool_choice="auto"
        )

        # 解析模型响应
        action = process_function_call (response)


        # 情况1：需要执行函数调用
        if action["type"] == "function":
            print("\n[系统] 检测到函数调用请求：")
            print(json.dumps(action, indent=2, ensure_ascii=False))

            # 执行对应函数
            if action["name"] == "get_current_weather":
                result = get_current_weather(**action["args"])
            elif action["name"] == "get_stock_price":
                result = get_stock_price(**action["args"])
            else:
                result = {"error": "未知函数"}

            print("\n[系统] 函数执行结果：")
            print(json.dumps(result, indent=2, default=str))

            # 第二步：将结果返回给模型生成最终回复
            second_response = openai.ChatCompletion.create(
                model="gpt-3.5-turbo-0613",
                messages=[
                    {"role": "user", "content": user_input},
                    {"role": "function", "name": action["name"], "content": json.dumps(result, ensure_ascii=False)}
                ]
            )

            print("\n[最终回复]" + second_response.choices[0].message["content"])

        #情况2：直接文本回复
        else:
            print("\n[直接回复]" + action["content"])

if __name__ == '__main__':
    chat_loop()
    