from typing import Optional, Any, Dict, List
from dataclasses import dataclass
from contextlib import AsyncExitStack
import json
import logging
from mcp.client.session import ClientSession
from mcp.client.sse import sse_client
from anyio.streams.memory import MemoryObjectReceiveStream
from urllib.parse import urlparse
from pydantic import BaseModel

@dataclass
class ToolParameter:
    """
    Represents a parameter for a tool.
    
    Attributes:
    par_name: Parameter name
    par_type: Parameter data type (e.g., "string", "number", "boolean")
    par_desc: Parameter description
    required: Whether the parameter is required
    default: Default value for the parameter
    """
    par_name: str
    par_type: str
    par_desc: str
    required: bool = False
    default: Any = None


@dataclass
class ToolDef:
    """
    Represents a tool definition.

    Attributes:
        tool_name: Tool name
        tool_desc: Tool description
        tool_pars: List of ToolParameter objects
        metadata: Optional dictionary of additional
        identifier: Tool identifier (defaults to name)
    """
    tool_name: str
    tool_desc: str
    tool_pars: List[ToolParameter]
    metadata: Optional[Dict[str, Any]] = None
    identifier: str = ""

@dataclass
class ToolInvocationResult:
    """
    Represents the result of a tool invocation.

    Attributes:
        content: Result content as a string
        error_code: Error code (0 for success, 1 for error)
    """
    content: str
    error_code: int


class MCP_Client:
    def __init__(self, host="127.0.0.1", port=8100):
        """
        Initialize MCP Client
        
        Args:
            host (str): 服务器主机地址，默认为 "127.0.0.1"
            port (int): 服务器端口号，默认为 8100
            
        Returns:
            None
            
        raises:
            None
            
        Attentions: 对于fastmcp，挂载点message不再/sse路径下，而是直接在/message路径下
        """
        self.url = f"http://{host}:{port}/sse"
        self.session: Optional[ClientSession] = None
        self.exit_stack = AsyncExitStack()

        # 配置日志系统
        logging.basicConfig(
            level=logging.DEBUG,
            format='%(asctime)s - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )

    async def connect(self):
        """Connect to MCP server using SSE transport"""
        try:
            # Create SSE client
            logging.debug("Creating SSE client...")
            self._streams_context = sse_client(url=self.url)
            streams = await self.exit_stack.enter_async_context(self._streams_context)

            # Create session
            logging.debug("Creating client session..")
            self._session_context = ClientSession(*streams)
            self.session = await self.exit_stack.enter_async_context(self._session_context)

            # Initialize session
            logging.debug("Initializing session...")
            await self.session.initialize()
            logging.info(f"connected to MCP server at {self.url}")

            # Verify connection by listing tools
            tools = await self.session.list_tools()
            logging.debug(f"Available tools: {[t.name for t in tools.tools]}")

        except Exception as e:
            logging.error(f"Connection failed: {str(e)}")
            await self.disconnect()
            raise ConnectionError(f"Failed to connect to MCP server: {str(e)}")
        
    async def disconnect(self):
        """Disconnect from MCP server"""
        await self.exit_stack.aclose()
        self.session = None
        logging.info("Disconnected from MCP server")

    async def list_tools(self) -> List[ToolDef]:
        """
        List available tools from the MCP endpoint

        Returns:
            List of ToolDef objects describing available tools
        """
        if not self.session:
            raise ConnectionError("Not connected to MCP server!")
        

        tools = []
        tools_result = await self.session.list_tools()
        for tool in tools_result.tools:
            parameters = []
            required = tool.inputSchema.get("required", [])
            for param_name, param_schema in tool.inputSchema.get("properties", {}).items():
                parameters.append(
                    ToolParameter(
                        par_name=param_name,
                        par_type=param_schema.get("type", "string"),
                        par_desc=param_schema.get("description", ""),
                        required=param_name in required,
                        default=param_schema.get("default"),
                    )
                )
            tools.append(
                ToolDef(
                    tool_name=tool.name,
                    tool_desc=tool.description,
                    tool_pars=parameters,
                    identifier=tool.name,
                )
            )
        return tools
    # tools




    async def call_tool(self, name: str, arguments: Optional[Dict[str, Any]] = None) -> ToolInvocationResult:
        """
        Call a tool by name

        Args:
            name: Name of the tool to invoke
            arguments: Dictionary of parameters to pass to the tool

        Returns:
            ToolInvocationResult containing the tool's response
        """
        if not self.session:
            raise ConnectionError("Not connected to MCP server")
        result = await self.session.call_tool(name, arguments or {})
        return ToolInvocationResult(
            content="\n".join([result.model_dump_json() for result in result.content]),
            error_code=1 if result.isError else 0,
        )


    async def get_prompt(self, name: str, **kwargs):
        """Get a prompt template"""
        if not self.session:
            raise ConnectionError("Not connected to MCP server")
        return await self.session.get_prompt(name, kwargs)

    async def get_resource(self, uri: str):
        """Get a resource"""
        if not self.session:
            raise ConnectionError("Not connected to MCP server")
        response = await self.session.read_resource(uri)
        for item in response:
            yield item
