import openai
import json
import configparser
from datetime import date, timedelta

cfgFileName = 'config.ini'

def load_config(path='config.ini'):
    config = configparser.ConfigParser()
    if not config.read(path):
        raise FileNotFoundError(f"配置文件 {path} 不存在或无法读取")
    if 'model' not in config:
        raise RuntimeError("配置文件缺少 [model] 部分")
    try:
        return {
            "api_key": config.get('model', 'API_KEY'),
            "base_url": config.get('model', 'BASE_URL'),
            "model_name": config.get('model', 'MODEL_NAME')
        }
    except (configparser.NoOptionError, configparser.NoSectionError) as e:
        raise RuntimeError(f"配置项读取失败: {e}")

cfg = load_config(cfgFileName)

# 获取配置文件中的API_KEY
api_key = cfg["api_key"]
base_url = cfg["base_url"]
model_name = cfg["model_name"]

# 最简单的一个聊天模型调用
client = openai.OpenAI(api_key=api_key, base_url=base_url)
test_response = client.chat.completions.create(
    model=model_name,
    messages=[
        {"role": "system", "content": "You are a helpful assistant"},
        {"role": "user", "content": "Hello"},
    ],
    stream=False
)

print(test_response.choices[0].message.content)


