:::::::::::::::::::::::::::::: 用于启动chatmodel_call_test.py程序::::::::::::::::::
rem 功能描述：
rem 纯文本大模型一般不具备function calling功能，例如：
rem GPT-3.5-turbo
rem qwen-turbo(https://bailian.console.aliyun.com/console?tab=model#/efm/model_experience_center/text?currentTab=textChat&modelId=qwen-turbo)
rem 或者 qwen coder 系列(https://bailian.console.aliyun.com/console?tab=model#/efm/model_experience_center/text?currentTab=textChat&modelId=qwen2.5-coder-7b-instruct)

:::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::
rem header part, prepare running environment and variables
@chcp 65001 >nul
@echo off
setlocal

set "cur_path=%cd%"
echo "working directory is set to %cur_path%"
cd /d "%cur_path%"
call D:\Work\python\venv310\Scripts\activate.bat

rem body part, main working snippet
python mcp_demo-host.py

endlocal

pause