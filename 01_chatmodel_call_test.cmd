:::::::::::::::::::::::::::::: 用于启动chatmodel_call_test.py程序::::::::::::::::::
rem 功能描述：
rem 主要用来验证大模型调用接口，实现一次简单的大模型调用

:::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::
rem header part, prepare running environment and variables
@chcp 65001 >nul
@echo off
setlocal

set cur_path=%cd%
echo %cur_path%
cd /d %cur_path%

rem body part, create python env and start working snippet
start "python 3.10.18" cmd /k "D:\Work\python\venv310\Scripts\activate.bat && python chatmodel_call_test.py"

endlocal

pause